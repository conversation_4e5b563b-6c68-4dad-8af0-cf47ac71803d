local mobile = script.Parent.Mobile
	local steeringWheel = mobile.Tap.SteeringWheel

	local dragging = false
	local lastAngle = 0
	local currentRotation = 0
	local maxRotation = 180
	local returnSpeed = 5
	local sensitivity = 0.7
	local center = Vector2.new()
	local lastInput = nil

	local function getDeltaAngle(a, b)
		local diff = a - b
		while diff > 180 do diff -= 360 end
		while diff < -180 do diff += 360 end
		return diff
	end

	steeringWheel.InputBegan:Connect(function(input)
		if input.UserInputType == Enum.UserInputType.Touch or input.UserInputType == Enum.UserInputType.MouseButton1 then
			dragging = true
			lastInput = input
			center = steeringWheel.AbsolutePosition + steeringWheel.AbsoluteSize / 2
			center = center - Vector2.new(0, 4)
			local mousePos = UserInputService:GetMouseLocation()
			lastAngle = math.deg(math.atan2(mousePos.Y - center.Y, mousePos.X - center.X))
		end
	end)

	UserInputService.InputEnded:Connect(function(input)
		if input == lastInput and input.UserInputType == Enum.UserInputType.Touch or input.UserInputType == Enum.UserInputType.MouseButton1 then
			dragging = false
		end
	end)

	-- Update rotation
	RunService.RenderStepped:Connect(function(dt)
		if dragging then
			local mousePos = UserInputService:GetMouseLocation()
			local angle = math.deg(math.atan2(mousePos.Y - center.Y, mousePos.X - center.X))
			local delta = getDeltaAngle(angle, lastAngle)
			lastAngle = angle

			currentRotation = math.clamp(currentRotation - delta * sensitivity, -maxRotation, maxRotation)
		else
			-- Smooth return to center
			currentRotation = currentRotation * math.clamp(1 - returnSpeed * dt, 0, 1)
		end

		steeringWheel.Rotation = currentRotation
		_GSteerT = currentRotation / maxRotation
	end)